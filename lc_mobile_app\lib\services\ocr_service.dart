import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import '../models/id_card_data_model.dart';
import '../utils/khmer_text_utils.dart';
import 'image_preprocessing_service.dart';

class OcrService {
  // Use TextRecognizer optimized for mixed English + Khmer text recognition
  // This configuration provides the best support for Cambodia ID cards
  final TextRecognizer _textRecognizer = TextRecognizer();
  final ImagePreprocessingService _preprocessingService =
      ImagePreprocessingService();

  // Enhanced recognition settings for better Khmer support
  static const bool _enableKhmerOptimization = true;
  static const bool _enableDetailedLogging = true;

  // Process image and extract text with enhanced Khmer support
  Future<String> extractTextFromImage(File imageFile) async {
    try {
      final inputImage = InputImage.fromFile(imageFile);
      final RecognizedText recognizedText = await _textRecognizer.processImage(
        inputImage,
      );

      String text = recognizedText.text;

      // Log the raw extracted text for debugging
      if (_enableDetailedLogging) {
        debugPrint('Raw OCR Text: $text');
        debugPrint('Contains Khmer: ${KhmerTextUtils.containsKhmerText(text)}');

        // Analyze the text for better debugging
        KhmerTextUtils.analyzeText(text, label: 'OCR Result');

        // Test ML Kit capabilities
        await _testMLKitCapabilities(recognizedText);
      }

      // Apply Khmer-specific optimizations if enabled
      if (_enableKhmerOptimization && KhmerTextUtils.containsKhmerText(text)) {
        text = _optimizeKhmerText(text, recognizedText);
      }

      return text;
    } catch (e) {
      debugPrint('Error extracting text: $e');
      return '';
    }
  }

  // Test ML Kit capabilities for Khmer text recognition
  Future<void> _testMLKitCapabilities(RecognizedText recognizedText) async {
    debugPrint('=== ML Kit Capabilities Test ===');

    // Test 1: Check text block structure
    debugPrint('Total text blocks: ${recognizedText.blocks.length}');

    for (int i = 0; i < recognizedText.blocks.length; i++) {
      final block = recognizedText.blocks[i];
      debugPrint('Block $i:');
      debugPrint('  Text: "${block.text}"');
      debugPrint('  Languages: ${block.recognizedLanguages.join(', ')}');
      debugPrint('  Lines: ${block.lines.length}');
      debugPrint(
        '  Contains Khmer: ${KhmerTextUtils.containsKhmerText(block.text)}',
      );

      // Test character-level analysis
      if (KhmerTextUtils.containsKhmerText(block.text)) {
        _analyzeKhmerCharacters(block.text);
      }
    }

    // Test 2: Check overall recognition quality
    final totalText = recognizedText.text;
    final khmerCharCount = _countKhmerCharacters(totalText);
    final latinCharCount = _countLatinCharacters(totalText);

    debugPrint('Character analysis:');
    debugPrint('  Khmer characters: $khmerCharCount');
    debugPrint('  Latin characters: $latinCharCount');
    debugPrint('  Mixed content: ${khmerCharCount > 0 && latinCharCount > 0}');
  }

  // Optimize Khmer text recognition results
  String _optimizeKhmerText(String text, RecognizedText recognizedText) {
    debugPrint('=== Khmer Text Optimization ===');

    String optimizedText = text;

    // Optimization 1: Fix common Khmer OCR errors
    optimizedText = _fixCommonKhmerOCRErrors(optimizedText);

    // Optimization 2: Improve text block ordering for mixed content
    optimizedText = _reorderMixedContent(recognizedText);

    // Optimization 3: Apply context-aware corrections
    optimizedText = _applyContextAwareCorrections(optimizedText);

    debugPrint('Original: "$text"');
    debugPrint('Optimized: "$optimizedText"');

    return optimizedText;
  }

  // Fix common Khmer OCR recognition errors
  String _fixCommonKhmerOCRErrors(String text) {
    String fixed = text;

    // Common Khmer character misrecognitions
    final khmerCorrections = {
      // Add common OCR error patterns for Khmer characters
      'ក្': 'ក', // Fix subscript issues
      'ង្': 'ង',
      'ច្': 'ច',
      'ញ្': 'ញ',
      'ត្': 'ត',
      'ន្': 'ន',
      'ប្': 'ប',
      'ម្': 'ម',
      'យ្': 'យ',
      'រ្': 'រ',
      'ល្': 'ល',
      'វ្': 'វ',
      'ស្': 'ស',
      'ហ្': 'ហ',
      'អ្': 'អ',
    };

    khmerCorrections.forEach((error, correction) {
      fixed = fixed.replaceAll(error, correction);
    });

    return fixed;
  }

  // Reorder mixed Khmer/Latin content for better readability
  String _reorderMixedContent(RecognizedText recognizedText) {
    List<String> orderedBlocks = [];

    for (final block in recognizedText.blocks) {
      // Separate Khmer and Latin content within each block
      if (KhmerTextUtils.containsKhmerText(block.text)) {
        final khmerPart = KhmerTextUtils.extractKhmerText(block.text);
        final latinPart = KhmerTextUtils.extractLatinText(block.text);

        // Add both parts if they exist
        if (khmerPart.isNotEmpty) orderedBlocks.add(khmerPart);
        if (latinPart.isNotEmpty) orderedBlocks.add(latinPart);
      } else {
        orderedBlocks.add(block.text);
      }
    }

    return orderedBlocks.join(' ');
  }

  // Apply context-aware corrections based on ID card structure
  String _applyContextAwareCorrections(String text) {
    String corrected = text;

    // Context 1: Fix ID number patterns
    corrected = corrected.replaceAllMapped(
      RegExp(r'ID\s*KHM\s*(\d+)'),
      (match) => 'IDKHM${match.group(1)}',
    );

    // Context 2: Fix common word separations
    corrected = corrected.replaceAll(RegExp(r'\s+'), ' ');

    // Context 3: Fix number/letter confusions in ID context
    if (corrected.contains('KHM')) {
      corrected = corrected.replaceAll('O', '0'); // O to 0 in ID numbers
      corrected = corrected.replaceAll('I', '1'); // I to 1 in ID numbers
    }

    return corrected.trim();
  }

  // Analyze Khmer characters for debugging
  void _analyzeKhmerCharacters(String text) {
    final khmerChars =
        text.runes.where((rune) {
          return rune >= 0x1780 && rune <= 0x17FF; // Khmer Unicode range
        }).toList();

    debugPrint('  Khmer character analysis:');
    debugPrint('    Count: ${khmerChars.length}');
    debugPrint(
      '    Unicode points: ${khmerChars.map((r) => '0x${r.toRadixString(16)}').join(', ')}',
    );
    debugPrint('    Characters: ${String.fromCharCodes(khmerChars)}');
  }

  // Count Khmer characters in text
  int _countKhmerCharacters(String text) {
    return text.runes.where((rune) => rune >= 0x1780 && rune <= 0x17FF).length;
  }

  // Count Latin characters in text
  int _countLatinCharacters(String text) {
    return text.runes
        .where(
          (rune) =>
              (rune >= 0x0041 && rune <= 0x005A) || // A-Z
              (rune >= 0x0061 && rune <= 0x007A), // a-z
        )
        .length;
  }

  // Enhanced ID number extraction with multiple patterns
  String? _extractIDNumber(String text) {
    debugPrint('=== ID Number Extraction ===');
    debugPrint('Analyzing text for ID patterns: "$text"');

    // Pattern 1: IDKHM followed by numbers (extract 9-digit number)
    RegExp idRegex = RegExp(r'IDKHM([0-9]+)');
    Match? idMatch = idRegex.firstMatch(text);
    if (idMatch != null && idMatch.groupCount >= 1) {
      final numbers = idMatch.group(1)?.trim() ?? '';
      // For Cambodian IDs, extract only the first 9 digits
      final cambodianIdNumber =
          numbers.length >= 9 ? numbers.substring(0, 9) : numbers;
      debugPrint(
        'Found ID pattern 1 (IDKHM): IDKHM$numbers -> Cambodian ID: $cambodianIdNumber',
      );
      return cambodianIdNumber;
    }

    // Pattern 2: ID KHM with spaces
    idRegex = RegExp(r'ID\s*KHM\s*([0-9]+)');
    idMatch = idRegex.firstMatch(text);
    if (idMatch != null && idMatch.groupCount >= 1) {
      final idNumber = 'IDKHM${idMatch.group(1)?.trim() ?? ''}';
      debugPrint('Found ID pattern 2 (ID KHM): $idNumber');
      return idNumber;
    }

    // Pattern 3: Letter followed by 10+ digits (like A0703698038)
    // Extract only the 9-digit number part for Cambodian IDs
    idRegex = RegExp(r'([A-Z])([0-9]{10,})');
    idMatch = idRegex.firstMatch(text);
    if (idMatch != null && idMatch.groupCount >= 2) {
      final letter = idMatch.group(1) ?? '';
      final numbers = idMatch.group(2) ?? '';
      // For Cambodian IDs, extract only the first 9 digits
      final cambodianIdNumber =
          numbers.length >= 9 ? numbers.substring(0, 9) : numbers;
      debugPrint(
        'Found ID pattern 3 (Letter+Numbers): $letter$numbers -> Cambodian ID: $cambodianIdNumber',
      );
      return cambodianIdNumber;
    }

    // Pattern 4: Just the number part (9 digits for Cambodian IDs)
    idRegex = RegExp(r'([0-9]{9})');
    final matches = idRegex.allMatches(text);
    for (final match in matches) {
      final number = match.group(1);
      if (number != null && number.length == 9) {
        // Skip if it looks like a year or date
        if (!_looksLikeYear(number) && !_looksLikeDate(number)) {
          debugPrint('Found ID pattern 4 (9-digit number): $number');
          return number;
        }
      }
    }

    debugPrint('No ID number pattern found');
    return null;
  }

  // Helper: Check if a number looks like a date
  bool _looksLikeDate(String text) {
    if (text.length == 8) {
      // DDMMYYYY or YYYYMMDD format
      final year1 = text.substring(4, 8); // DDMMYYYY
      final year2 = text.substring(0, 4); // YYYYMMDD
      return _looksLikeYear(year1) || _looksLikeYear(year2);
    }
    return false;
  }

  // Clean OCR text by removing common artifacts and improving readability
  String _cleanOCRText(String text) {
    return text
        // Remove common OCR artifacts
        .replaceAll(RegExp(r'[<>]+'), '') // Remove < and > characters
        .replaceAll(RegExp(r'[|]+'), 'I') // Replace | with I
        .replaceAll(RegExp(r'[0O]+(?=[A-Z])'), 'O') // Fix 0/O confusion in text
        .replaceAll(RegExp(r'[1l]+(?=[A-Z])'), 'I') // Fix 1/l/I confusion
        // Clean up whitespace
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();
  }

  // Enhanced date extraction for Cambodia ID cards
  String? _extractDateOfBirth(String text, List<TextBlock> textBlocks) {
    debugPrint('=== Date Extraction Analysis ===');
    debugPrint('Full text for date search: "$text"');

    // Clean the text first to remove OCR artifacts
    String cleanedText = _cleanOCRText(text);
    debugPrint('Cleaned text: "$cleanedText"');

    // Multiple date patterns for Cambodia ID cards - more specific to avoid ID numbers
    List<RegExp> datePatterns = [
      // Pattern 1: DD-MM-YYYY or DD/MM/YYYY or DD.MM.YYYY (with separators)
      RegExp(r'(?<!\d)(\d{1,2})[\-\.\/](\d{1,2})[\-\.\/](\d{4})(?!\d)'),
      // Pattern 2: DD MM YYYY (with spaces, not part of longer number sequence)
      RegExp(r'(?<!\d)(\d{1,2})\s+(\d{1,2})\s+(\d{4})(?!\d)'),
      // Pattern 3: DD-MM-YY (2-digit year with separators)
      RegExp(r'(?<!\d)(\d{1,2})[\-\.\/](\d{1,2})[\-\.\/](\d{2})(?!\d)'),
      // Pattern 4: DDMMYYYY (8 digits, but not part of longer sequence like ID numbers)
      RegExp(r'(?<!\d)(\d{2})(\d{2})(\d{4})(?!\d)'),
      // Pattern 5: Look for dates near birth-related keywords
      RegExp(
        r'(?:birth|born|dob|date)[\s:]*(\d{1,2})[\-\.\/\s](\d{1,2})[\-\.\/\s](\d{2,4})',
        caseSensitive: false,
      ),
    ];

    // Try each pattern on both original and cleaned text
    List<String> textsToSearch = [cleanedText, text];

    for (String searchText in textsToSearch) {
      for (int i = 0; i < datePatterns.length; i++) {
        final pattern = datePatterns[i];
        final matches = pattern.allMatches(searchText);

        debugPrint(
          'Pattern ${i + 1}: ${pattern.pattern} - Found ${matches.length} matches in ${searchText == cleanedText ? "cleaned" : "original"} text',
        );

        for (final match in matches) {
          if (match.groupCount >= 3) {
            String day = match.group(1) ?? '';
            String month = match.group(2) ?? '';
            String year = match.group(3) ?? '';

            // Handle 2-digit years
            if (year.length == 2) {
              int yearInt = int.tryParse(year) ?? 0;
              if (yearInt > 50) {
                year = '19$year';
              } else {
                year = '20$year';
              }
            }

            debugPrint('Found date candidate: $day-$month-$year');

            // Enhanced validation - check if this looks like a birth date
            if (_isValidBirthDate(day, month, year, searchText)) {
              final formattedDate =
                  '${day.padLeft(2, '0')}-${month.padLeft(2, '0')}-$year';
              debugPrint('Valid birth date found: $formattedDate');
              return formattedDate;
            }
          }
        }
      }
    }

    // Try searching in individual text blocks with cleaned text
    debugPrint('Searching in individual text blocks...');
    for (int blockIndex = 0; blockIndex < textBlocks.length; blockIndex++) {
      final block = textBlocks[blockIndex];
      final cleanedBlockText = _cleanOCRText(block.text);
      debugPrint(
        'Block $blockIndex: "${block.text}" -> cleaned: "$cleanedBlockText"',
      );

      List<String> blockTextsToSearch = [cleanedBlockText, block.text];

      for (String blockSearchText in blockTextsToSearch) {
        for (int i = 0; i < datePatterns.length; i++) {
          final pattern = datePatterns[i];
          final matches = pattern.allMatches(blockSearchText);

          for (final match in matches) {
            if (match.groupCount >= 3) {
              String day = match.group(1) ?? '';
              String month = match.group(2) ?? '';
              String year = match.group(3) ?? '';

              // Handle 2-digit years
              if (year.length == 2) {
                int yearInt = int.tryParse(year) ?? 0;
                if (yearInt > 50) {
                  year = '19$year';
                } else {
                  year = '20$year';
                }
              }

              debugPrint('Block date candidate: $day-$month-$year');

              if (_isValidBirthDate(day, month, year, blockSearchText)) {
                final formattedDate =
                    '${day.padLeft(2, '0')}-${month.padLeft(2, '0')}-$year';
                debugPrint('Valid birth date found in block: $formattedDate');
                return formattedDate;
              }
            }
          }
        }
      }
    }

    debugPrint('No valid date found');
    return null;
  }

  // Enhanced birth date validation that considers context and avoids ID numbers
  bool _isValidBirthDate(
    String day,
    String month,
    String year,
    String context,
  ) {
    // First do basic date validation
    if (!_isValidDate(day, month, year)) {
      return false;
    }

    final int? dayInt = int.tryParse(day);
    final int? monthInt = int.tryParse(month);
    final int? yearInt = int.tryParse(year);

    if (dayInt == null || monthInt == null || yearInt == null) {
      return false;
    }

    // Additional birth date specific validation
    final currentYear = DateTime.now().year;

    // Birth year should be reasonable (not too old, not in future)
    if (yearInt < 1900 || yearInt > currentYear) {
      debugPrint('Invalid birth year: $yearInt');
      return false;
    }

    // Check if this date appears to be part of an ID number sequence
    String dateString = '$day$month$year';
    if (dateString.length >= 8) {
      // If this 8+ digit sequence appears in context with "IDKHM", it's likely an ID number
      if (context.contains('IDKHM$dateString') ||
          context.contains('KHM$dateString') ||
          context.contains('ID$dateString')) {
        debugPrint('Date appears to be part of ID number: $dateString');
        return false;
      }
    }

    // Check for unrealistic birth dates (too young or too old for ID card)
    final age = currentYear - yearInt;
    if (age < 5 || age > 120) {
      debugPrint('Unrealistic age: $age years');
      return false;
    }

    return true;
  }

  // Validate if the extracted date components form a valid date
  bool _isValidDate(String day, String month, String year) {
    final int? dayInt = int.tryParse(day);
    final int? monthInt = int.tryParse(month);
    final int? yearInt = int.tryParse(year);

    if (dayInt == null || monthInt == null || yearInt == null) {
      return false;
    }

    // Basic validation
    if (yearInt < 1900 || yearInt > DateTime.now().year) return false;
    if (monthInt < 1 || monthInt > 12) return false;
    if (dayInt < 1 || dayInt > 31) return false;

    // More specific validation for days in month
    if (monthInt == 2) {
      // February
      bool isLeapYear =
          (yearInt % 4 == 0 && yearInt % 100 != 0) || (yearInt % 400 == 0);
      if (dayInt > (isLeapYear ? 29 : 28)) return false;
    } else if ([4, 6, 9, 11].contains(monthInt)) {
      // April, June, September, November
      if (dayInt > 30) return false;
    }

    return true;
  }

  // Get all text blocks from the image
  Future<List<TextBlock>> getTextBlocks(File imageFile) async {
    try {
      final inputImage = InputImage.fromFile(imageFile);
      final RecognizedText recognizedText = await _textRecognizer.processImage(
        inputImage,
      );

      return recognizedText.blocks;
    } catch (e) {
      debugPrint('Error getting text blocks: $e');
      return [];
    }
  }

  // Parse ID card data from extracted text with automatic preprocessing
  Future<IDCardData> extractIDCardData(File imageFile) async {
    try {
      debugPrint('Starting ID card data extraction...');

      // Step 1: Preprocess the image for better OCR
      debugPrint('Preprocessing image for optimal OCR...');
      final File processedImage = await _preprocessingService.preprocessForOCR(
        imageFile,
      );

      // Step 2: Extract text from the processed image
      final String extractedText = await extractTextFromImage(processedImage);
      final List<TextBlock> textBlocks = await getTextBlocks(processedImage);

      // Step 3: Try to detect if this is a Cambodia ID card
      final bool isCambodianID = _isCambodianIDCard(extractedText);
      debugPrint('Detected as Cambodian ID: $isCambodianID');

      if (isCambodianID) {
        return _extractCambodianIDCardData(extractedText, textBlocks);
      } else {
        return _extractGenericIDCardData(extractedText);
      }
    } catch (e) {
      debugPrint('Error extracting ID card data: $e');
      return IDCardData.empty();
    }
  }

  // Check if the text is from a Cambodian ID card
  bool _isCambodianIDCard(String text) {
    debugPrint('=== Cambodian ID Detection Analysis ===');
    debugPrint('Analyzing text: "$text"');

    // Look for specific Cambodian ID card markers
    final bool hasKhmerText = RegExp(
      r'[\u1780-\u17FF]',
    ).hasMatch(text); // Khmer Unicode range
    debugPrint('Has Khmer text: $hasKhmerText');

    final bool hasIDKHM = text.contains('IDKHM') || text.contains('KHM');
    debugPrint('Has IDKHM/KHM: $hasIDKHM');

    final bool hasCambodia =
        text.toLowerCase().contains('cambodia') ||
        text.toLowerCase().contains('cambodian');
    debugPrint('Has Cambodia text: $hasCambodia');

    // Enhanced detection patterns for Cambodian IDs

    // Pattern 1: Look for Cambodian name patterns (common Cambodian names)
    final bool hasCambodianNamePattern = _hasCambodianNamePattern(text);
    debugPrint('Has Cambodian name pattern: $hasCambodianNamePattern');

    // Pattern 2: Look for MRZ-like patterns typical of Cambodian IDs
    final bool hasMRZPattern = _hasCambodianMRZPattern(text);
    debugPrint('Has MRZ pattern: $hasMRZPattern');

    // Pattern 3: Look for ID number patterns (9-digit numbers common in Cambodian IDs)
    final bool hasIDNumberPattern = _hasCambodianIDNumberPattern(text);
    debugPrint('Has ID number pattern: $hasIDNumberPattern');

    // Pattern 4: Look for specific character sequences that appear in Cambodian ID OCR
    final bool hasOCRArtifacts = _hasCambodianOCRArtifacts(text);
    debugPrint('Has OCR artifacts: $hasOCRArtifacts');

    final bool isCambodianID =
        hasKhmerText ||
        hasIDKHM ||
        hasCambodia ||
        hasCambodianNamePattern ||
        hasMRZPattern ||
        hasIDNumberPattern ||
        hasOCRArtifacts;

    debugPrint('Final decision - Is Cambodian ID: $isCambodianID');
    return isCambodianID;
  }

  // Check for Cambodian name patterns in Latin script
  bool _hasCambodianNamePattern(String text) {
    // Common Cambodian name prefixes and patterns
    final cambodianNamePatterns = [
      // Common Cambodian name prefixes
      r'\bSOK\b', r'\bCHAN\b', r'\bLIM\b', r'\bKIM\b', r'\bSOM\b',
      r'\bPENG\b', r'\bHENG\b', r'\bKEO\b', r'\bSIN\b', r'\bTAN\b',
      r'\bVAN\b', r'\bSAM\b', r'\bMOM\b', r'\bNEANG\b', r'\bSREY\b',
      r'\bCHEA\b', r'\bSOPHEA\b', r'\bRATA\b', r'\bVICHEA\b',
      r'\bSOKHA\b', r'\bPISACH\b', r'\bCHANTHEA\b', r'\bSOPHAL\b',
      r'\bEN\b', r'\bOUN\b', r'\bCHHAY\b', r'\bVIRY\b',

      // Common Cambodian name endings
      r'PHAL\b', r'PHEA\b', r'REAKSA\b', r'RITH\b', r'RITHY\b',
      r'VUTH\b', r'VUTHY\b', r'REACH\b', r'ROEUN\b', r'ROTHA\b',
      r'THEA\b', r'THEARY\b', r'MONY\b', r'MEAS\b', r'MEAN\b',
    ];

    for (String pattern in cambodianNamePatterns) {
      if (RegExp(pattern, caseSensitive: false).hasMatch(text)) {
        debugPrint('Found Cambodian name pattern: $pattern');
        return true;
      }
    }

    return false;
  }

  // Check for Cambodian MRZ patterns
  bool _hasCambodianMRZPattern(String text) {
    // Look for patterns like: digits + M/F + digits + KHM
    // Or: YYMMDD + digit + M/F patterns
    final mrzPatterns = [
      r'\d{6}\d[MF]', // YYMMDD + digit + gender (like 8910059M)
      r'\d{6}[MF]\d{6}KHM', // Traditional MRZ format
      r'[A-Z]\d{10}', // Letter followed by 10 digits
      r'\d{3}[A-Z]\d{6}', // 3 digits + letter + 6 digits
    ];

    for (String pattern in mrzPatterns) {
      if (RegExp(pattern).hasMatch(text)) {
        debugPrint('Found MRZ pattern: $pattern');
        return true;
      }
    }

    return false;
  }

  // Check for Cambodian ID number patterns
  bool _hasCambodianIDNumberPattern(String text) {
    // Cambodian ID numbers are typically 9 digits
    // Look for 9-digit sequences that could be ID numbers
    final idPatterns = [
      r'\b\d{9}\b', // Exactly 9 digits
      r'[A-Z]\d{10}', // Letter + 10 digits (like A0703698038)
      r'\d{10}', // 10 digits (extended format)
    ];

    for (String pattern in idPatterns) {
      final matches = RegExp(pattern).allMatches(text);
      for (final match in matches) {
        final matchedText = match.group(0) ?? '';
        // Avoid matching years or other non-ID numbers
        if (!_looksLikeYear(matchedText) &&
            !_looksLikePhoneNumber(matchedText)) {
          debugPrint('Found ID number pattern: $matchedText');
          return true;
        }
      }
    }

    return false;
  }

  // Check for OCR artifacts common in Cambodian ID scanning
  bool _hasCambodianOCRArtifacts(String text) {
    // Common OCR artifacts from Cambodian ID cards
    final artifacts = [
      r'<{3,}', // Multiple < characters (MRZ artifacts)
      r'>{3,}', // Multiple > characters
      r'[A-Z]\d{10}<+', // Letter + digits + < characters
      r'\d+%\d+', // Numbers with % (OCR misreading)
      r'[A-Z]{2,3}\d{6,}', // 2-3 letters followed by 6+ digits
    ];

    for (String pattern in artifacts) {
      if (RegExp(pattern).hasMatch(text)) {
        debugPrint('Found OCR artifact pattern: $pattern');
        return true;
      }
    }

    return false;
  }

  // Helper: Check if a number looks like a year
  bool _looksLikeYear(String text) {
    final year = int.tryParse(text);
    return year != null && year >= 1900 && year <= DateTime.now().year + 10;
  }

  // Helper: Check if a number looks like a phone number
  bool _looksLikePhoneNumber(String text) {
    // Cambodian phone numbers typically start with specific prefixes
    return text.startsWith(RegExp(r'(855|012|017|010|092|096|098|099)'));
  }

  // Extract data specifically from Cambodian ID cards with enhanced Khmer support
  IDCardData _extractCambodianIDCardData(
    String extractedText,
    List<TextBlock> textBlocks,
  ) {
    IDCardData idCardData = IDCardData.empty();

    // Log all text blocks for debugging
    debugPrint('=== Text Blocks Analysis ===');
    for (int i = 0; i < textBlocks.length; i++) {
      final block = textBlocks[i];
      KhmerTextUtils.analyzeText(block.text, label: 'Block $i');
    }

    // Extract ID number with multiple patterns
    String? idNumber = _extractIDNumber(extractedText);
    if (idNumber != null) {
      idCardData = idCardData.copyWith(idNumber: idNumber);
    }

    // Extract name - look for text near the top of the card after Khmer text
    // This is challenging due to Khmer script, so we'll use position heuristics
    String? name = _extractName(textBlocks, extractedText);
    if (name != null) {
      idCardData = idCardData.copyWith(fullName: name);
    }

    // Extract date of birth using enhanced method (including MRZ parsing)
    String? dateOfBirth =
        _extractDateOfBirth(extractedText, textBlocks) ??
        _extractDateFromMRZ(extractedText);
    if (dateOfBirth != null) {
      idCardData = idCardData.copyWith(dateOfBirth: dateOfBirth);
    }

    // Validate and cross-reference extracted data
    idCardData = _validateAndCrossReference(idCardData, extractedText);

    // Extract nationality (almost always "Cambodian" or "CAMBODIAN")
    idCardData = idCardData.copyWith(nationality: 'Cambodian');

    // Extract gender based on common patterns in Cambodian IDs and MRZ
    String? gender = _extractGenderFromMRZ(extractedText);
    if (gender == null) {
      final RegExp maleRegex = RegExp(r'\bM\b|\bMALE\b', caseSensitive: false);
      final RegExp femaleRegex = RegExp(
        r'\bF\b|\bFEMALE\b',
        caseSensitive: false,
      );

      if (maleRegex.hasMatch(extractedText)) {
        gender = 'Male';
      } else if (femaleRegex.hasMatch(extractedText)) {
        gender = 'Female';
      }
    }

    if (gender != null) {
      idCardData = idCardData.copyWith(gender: gender);
    }

    // Extract address - this is challenging due to Khmer script
    // We'll look for text blocks that might contain address information
    for (final block in textBlocks) {
      // Skip very short blocks or blocks with ID numbers
      if (block.text.length < 5 || RegExp(r'IDKHM').hasMatch(block.text)) {
        continue;
      }

      // Look for blocks with mixed text that might be addresses
      if (!KhmerTextUtils.containsKhmerText(block.text) && // Not Khmer
          block.text.contains(RegExp(r'[a-zA-Z]')) && // Has Latin letters
          !block.text.toUpperCase().contains('KHM') && // Not the country code
          !block.text.contains(RegExp(r'^\d+$'))) {
        // Not just numbers

        // Check if it's not already captured as name
        if (block.text.trim() != idCardData.fullName) {
          idCardData = idCardData.copyWith(address: block.text.trim());
          break;
        }
      }
    }

    return idCardData;
  }

  // Extract data from generic ID cards
  IDCardData _extractGenericIDCardData(String extractedText) {
    IDCardData idCardData = IDCardData.empty();

    // Extract ID number (assuming format like "ID: 123456789")
    final RegExp idRegex = RegExp(r'ID[:\s]*([0-9]+)');
    final idMatch = idRegex.firstMatch(extractedText);
    if (idMatch != null && idMatch.groupCount >= 1) {
      idCardData = idCardData.copyWith(
        idNumber: idMatch.group(1)?.trim() ?? '',
      );
    }

    // Extract name (assuming format like "Name: John Doe")
    final RegExp nameRegex = RegExp(r'Name[:\s]*([\w\s]+)');
    final nameMatch = nameRegex.firstMatch(extractedText);
    if (nameMatch != null && nameMatch.groupCount >= 1) {
      idCardData = idCardData.copyWith(
        fullName: nameMatch.group(1)?.trim() ?? '',
      );
    }

    // Extract date of birth (assuming format like "DOB: DD/MM/YYYY")
    final RegExp dobRegex = RegExp(
      r'(DOB|Date of Birth)[:\s]*(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})',
    );
    final dobMatch = dobRegex.firstMatch(extractedText);
    if (dobMatch != null && dobMatch.groupCount >= 2) {
      idCardData = idCardData.copyWith(
        dateOfBirth: dobMatch.group(2)?.trim() ?? '',
      );
    }

    // Extract address (assuming format like "Address: 123 Main St")
    final RegExp addressRegex = RegExp(r'Address[:\s]*([\w\s,\.]+)');
    final addressMatch = addressRegex.firstMatch(extractedText);
    if (addressMatch != null && addressMatch.groupCount >= 1) {
      idCardData = idCardData.copyWith(
        address: addressMatch.group(1)?.trim() ?? '',
      );
    }

    // Extract gender (assuming format like "Gender: Male")
    final RegExp genderRegex = RegExp(r'Gender[:\s]*(Male|Female|M|F)');
    final genderMatch = genderRegex.firstMatch(extractedText);
    if (genderMatch != null && genderMatch.groupCount >= 1) {
      idCardData = idCardData.copyWith(gender: genderMatch.group(1)?.trim());
    }

    // Extract nationality (assuming format like "Nationality: Cambodian")
    final RegExp nationalityRegex = RegExp(r'Nationality[:\s]*([\w\s]+)');
    final nationalityMatch = nationalityRegex.firstMatch(extractedText);
    if (nationalityMatch != null && nationalityMatch.groupCount >= 1) {
      idCardData = idCardData.copyWith(
        nationality: nationalityMatch.group(1)?.trim(),
      );
    }

    return idCardData;
  }

  // Enhanced name extraction with OCR artifact cleaning
  String? _extractName(List<TextBlock> textBlocks, String fullText) {
    debugPrint('=== Name Extraction Analysis ===');

    List<String> nameCandidates = [];

    for (int i = 0; i < textBlocks.length; i++) {
      final block = textBlocks[i];
      final cleanedText = _cleanOCRText(block.text);

      debugPrint('Block $i: "${block.text}" -> cleaned: "$cleanedText"');

      // Skip Khmer text blocks
      if (KhmerTextUtils.containsKhmerText(block.text)) {
        debugPrint('Skipping Khmer text block');
        continue;
      }

      // Skip blocks that are clearly not names
      if (cleanedText.length < 3 ||
          cleanedText.contains('KHM') ||
          cleanedText.contains('IDKHM') ||
          RegExp(r'^\d+$').hasMatch(cleanedText) || // Only numbers
          RegExp(r'\d{4}').hasMatch(cleanedText)) {
        // Contains year-like numbers
        debugPrint('Skipping non-name block: $cleanedText');
        continue;
      }

      // Look for name-like text patterns
      if (_isNameLike(cleanedText)) {
        nameCandidates.add(cleanedText);
        debugPrint('Found name candidate: $cleanedText');
      }
    }

    // Return the best name candidate
    if (nameCandidates.isNotEmpty) {
      // Prefer longer names that look more complete
      nameCandidates.sort((a, b) => b.length.compareTo(a.length));
      String bestName = nameCandidates.first;
      debugPrint('Selected best name: $bestName');
      return bestName;
    }

    debugPrint('No name found');
    return null;
  }

  // Check if text looks like a name
  bool _isNameLike(String text) {
    // Names are typically:
    // - All uppercase (on ID cards)
    // - Contain only letters and spaces
    // - Between 3-50 characters
    // - Don't contain numbers or special characters (except spaces)

    if (text.length < 3 || text.length > 50) return false;

    // Should be mostly letters
    if (!RegExp(r'^[A-Z\s]+$').hasMatch(text.toUpperCase())) return false;

    // Should not be all the same character
    if (RegExp(r'^(.)\1+$').hasMatch(text)) return false;

    // Should have at least one letter
    if (!RegExp(r'[A-Za-z]').hasMatch(text)) return false;

    // Common non-name patterns to exclude
    List<String> excludePatterns = [
      'CAMBODIA',
      'KINGDOM',
      'CARD',
      'IDENTITY',
      'NATIONAL',
      'MALE',
      'FEMALE',
      'DATE',
      'BIRTH',
      'ADDRESS',
      'NATIONALITY',
    ];

    for (String pattern in excludePatterns) {
      if (text.toUpperCase().contains(pattern)) return false;
    }

    return true;
  }

  // Extract date of birth from Machine Readable Zone (MRZ)
  String? _extractDateFromMRZ(String text) {
    debugPrint('=== MRZ Date Extraction ===');
    debugPrint('Analyzing text for MRZ patterns: "$text"');

    // Cambodian ID MRZ format research based on user findings:
    // Pattern: 8910059M = 891005 (birth: 1989-10-05) + 9 (unknown) + M (gender)
    // IDKHM15056O9053 = IDKHM + 150569053 (9-digit ID)

    // Pattern 1: Look for YYMMDD + digit + M/F pattern (user's example)
    final RegExp pattern1 = RegExp(r'(\d{6})\d[MF]');
    final match1 = pattern1.firstMatch(text);

    if (match1 != null) {
      final birthDateStr = match1.group(1);
      debugPrint('Found MRZ birth date pattern 1: $birthDateStr');

      if (birthDateStr != null && birthDateStr.length == 6) {
        final parsedDate = _parseMRZDate(birthDateStr);
        if (parsedDate != null) {
          debugPrint('✅ Valid MRZ birth date (pattern 1): $parsedDate');
          return parsedDate;
        }
      }
    }

    // Pattern 2: Traditional MRZ format: YYMMDD + M/F + YYMMDD + KHM
    final RegExp pattern2 = RegExp(r'(\d{6})[MF](\d{6})KHM');
    final match2 = pattern2.firstMatch(text);

    if (match2 != null) {
      final birthDateStr = match2.group(1);
      debugPrint('Found MRZ birth date pattern 2: $birthDateStr');

      if (birthDateStr != null && birthDateStr.length == 6) {
        final parsedDate = _parseMRZDate(birthDateStr);
        if (parsedDate != null) {
          debugPrint('✅ Valid MRZ birth date (pattern 2): $parsedDate');
          return parsedDate;
        }
      }
    }

    // Pattern 3: Look for any 6-digit sequence that could be a birth date
    final RegExp pattern3 = RegExp(r'(\d{6})');
    final matches3 = pattern3.allMatches(text);

    for (final match in matches3) {
      final dateStr = match.group(1);
      if (dateStr != null && dateStr.length == 6) {
        // Skip if it's likely an ID number (starts with 1-3 for recent years)
        if (dateStr.startsWith(RegExp(r'[123]'))) continue;

        final parsedDate = _parseMRZDate(dateStr);
        if (parsedDate != null) {
          debugPrint(
            '✅ Valid MRZ birth date (pattern 3): $parsedDate from $dateStr',
          );
          return parsedDate;
        }
      }
    }

    debugPrint('❌ No valid MRZ birth date found');
    return null;
  }

  // Parse YYMMDD format to DD-MM-YYYY
  String? _parseMRZDate(String yymmdd) {
    if (yymmdd.length != 6) return null;

    try {
      final year = yymmdd.substring(0, 2);
      final month = yymmdd.substring(2, 4);
      final day = yymmdd.substring(4, 6);

      // Convert 2-digit year to 4-digit year
      int yearInt = int.tryParse(year) ?? 0;
      String fullYear;

      // Smart year conversion based on reasonable birth year ranges
      if (yearInt >= 30 && yearInt <= 99) {
        fullYear = '19$year'; // 1930-1999
      } else if (yearInt >= 0 && yearInt <= 29) {
        fullYear = '20$year'; // 2000-2029
      } else {
        return null;
      }

      debugPrint('Parsing MRZ date: $yymmdd -> $day-$month-$fullYear');

      // Validate the date components
      if (_isValidDate(day, month, fullYear)) {
        return '${day.padLeft(2, '0')}-${month.padLeft(2, '0')}-$fullYear';
      } else {
        debugPrint('Invalid date components: $day-$month-$fullYear');
        return null;
      }
    } catch (e) {
      debugPrint('Error parsing MRZ date: $e');
      return null;
    }
  }

  // Extract gender from Machine Readable Zone (MRZ)
  String? _extractGenderFromMRZ(String text) {
    debugPrint('=== MRZ Gender Extraction ===');
    debugPrint('Analyzing text for gender patterns: "$text"');

    // Pattern 1: Look for YYMMDD + digit + M/F pattern (user's example: 8910059M)
    final RegExp pattern1 = RegExp(r'\d{6}\d([MF])');
    final match1 = pattern1.firstMatch(text);

    if (match1 != null) {
      final genderCode = match1.group(1);
      debugPrint('Found MRZ gender pattern 1: $genderCode');

      if (genderCode == 'M') {
        return 'Male';
      } else if (genderCode == 'F') {
        return 'Female';
      }
    }

    // Pattern 2: Traditional MRZ format: 6 digits + M/F + 6 digits + KHM
    final RegExp pattern2 = RegExp(r'\d{6}([MF])\d{6}KHM');
    final match2 = pattern2.firstMatch(text);

    if (match2 != null) {
      final genderCode = match2.group(1);
      debugPrint('Found MRZ gender pattern 2: $genderCode');

      if (genderCode == 'M') {
        return 'Male';
      } else if (genderCode == 'F') {
        return 'Female';
      }
    }

    debugPrint('❌ No MRZ gender pattern found');
    return null;
  }

  // Validate and cross-reference extracted data for consistency
  IDCardData _validateAndCrossReference(IDCardData data, String fullText) {
    debugPrint('=== Data Validation and Cross-Reference ===');

    // Cross-reference ID number with MRZ
    if (data.idNumber.isNotEmpty && fullText.contains('KHM')) {
      final mrzIdMatch = RegExp(r'IDKHM(\d+)').firstMatch(fullText);
      if (mrzIdMatch != null) {
        final mrzId = 'IDKHM${mrzIdMatch.group(1)}';
        if (data.idNumber != mrzId) {
          debugPrint('⚠️ ID number mismatch: ${data.idNumber} vs $mrzId');
        } else {
          debugPrint('✅ ID number validated against MRZ');
        }
      }
    }

    // Validate name consistency (check for duplicate names in different formats)
    if (data.fullName.isNotEmpty) {
      final nameWords = data.fullName.split(' ');
      bool nameConsistent = true;

      // Check if name appears in multiple blocks consistently
      for (String word in nameWords) {
        if (word.length > 2 &&
            !fullText.toUpperCase().contains(word.toUpperCase())) {
          nameConsistent = false;
          break;
        }
      }

      if (nameConsistent) {
        debugPrint('✅ Name consistency validated');
      } else {
        debugPrint('⚠️ Name consistency check failed');
      }
    }

    // Validate date format and reasonableness
    if (data.dateOfBirth.isNotEmpty) {
      try {
        final parts = data.dateOfBirth.split('-');
        if (parts.length == 3) {
          final day = int.parse(parts[0]);
          final month = int.parse(parts[1]);
          final year = int.parse(parts[2]);

          final age = DateTime.now().year - year;
          if (age >= 18 && age <= 100) {
            debugPrint('✅ Birth date validated (age: $age)');
          } else {
            debugPrint('⚠️ Unusual age detected: $age years');
          }
        }
      } catch (e) {
        debugPrint('⚠️ Date format validation failed: $e');
      }
    }

    // Calculate confidence score based on extracted fields
    int confidence = 0;
    if (data.idNumber.isNotEmpty) confidence += 25;
    if (data.fullName.isNotEmpty) confidence += 25;
    if (data.dateOfBirth.isNotEmpty) confidence += 25;
    if (data.gender != null) confidence += 15;
    if (data.nationality?.isNotEmpty == true) confidence += 10;

    debugPrint('📊 Extraction confidence: $confidence%');

    return data;
  }

  // Dispose resources
  void dispose() {
    _textRecognizer.close();
  }
}
