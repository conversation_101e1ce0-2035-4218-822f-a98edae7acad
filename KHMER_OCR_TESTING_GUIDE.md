# Khmer Text Recognition Testing Guide

## Overview
This guide provides comprehensive instructions for testing Khmer text recognition capabilities using both native ML Kit examples and your Flutter implementation.

## 🎯 Testing Strategy

### 1. Native ML Kit Testing (Recommended First Step)

#### Option A: Use Google's Official ML Kit Sample Apps

1. **Clone Google's ML Kit Repository:**
   ```bash
   git clone https://github.com/googlesamples/mlkit.git
   cd mlkit/android/vision-quickstart
   ```

2. **Build and Run the Sample App:**
   - Open the project in Android Studio
   - Build and run on your device
   - Test with Cambodian ID card images
   - Check logcat for detailed recognition results

#### Option B: Use the Custom Native Test App (Provided)

1. **Create a new Android project** with the files provided:
   - `MainActivity.java`
   - `activity_main.xml`
   - `build.gradle`
   - `AndroidManifest.xml`

2. **Key Features of the Test App:**
   - Camera capture and image selection
   - Detailed ML Kit capability analysis
   - Khmer character detection and counting
   - Language recognition testing
   - Unicode point analysis

3. **Testing Process:**
   ```
   1. Install the test app on your device
   2. Capture or select a Cambodian ID card image
   3. Review the detailed analysis in the results
   4. Check Android logcat for comprehensive debugging info
   ```

### 2. Flutter Implementation Testing

#### Enhanced OCR Service Features

Your updated `ocr_service.dart` now includes:

- **Khmer-specific optimizations** (`_enableKhmerOptimization`)
- **Detailed logging** (`_enableDetailedLogging`)
- **ML Kit capability testing** (`_testMLKitCapabilities`)
- **Khmer text optimization** (`_optimizeKhmerText`)
- **Common OCR error corrections** (`_fixCommonKhmerOCRErrors`)

#### Testing Steps

1. **Enable Detailed Logging:**
   ```dart
   static const bool _enableDetailedLogging = true;
   ```

2. **Test with Sample Images:**
   - Use high-quality Cambodian ID card images
   - Test with different lighting conditions
   - Try various angles and orientations

3. **Monitor Debug Output:**
   ```
   Look for these debug messages:
   - "=== ML Kit Capabilities Test ==="
   - "=== Khmer Text Optimization ==="
   - "Khmer character analysis:"
   - "Character analysis:"
   ```

## 🔍 Key Testing Areas

### 1. Khmer Character Recognition

**Test Cases:**
- Pure Khmer text blocks
- Mixed Khmer/Latin text
- Khmer text with special characters
- Different Khmer fonts and styles

**Expected Results:**
- Unicode range: U+1780-U+17FF
- Proper character separation
- Correct diacritic handling

### 2. ID Card Specific Testing

**Cambodian ID Card Elements:**
- ID number patterns (IDKHM + 9 digits)
- MRZ format (YYMMDD + digit + M/F)
- Name recognition (Latin script)
- Date extraction (various formats)

**Test Scenarios:**
```
1. Clear, well-lit ID card
2. Slightly blurred image
3. Different angles (up to 15 degrees)
4. Various lighting conditions
5. Worn or damaged ID cards
```

### 3. Performance Testing

**Metrics to Monitor:**
- Recognition accuracy percentage
- Processing time per image
- Memory usage during OCR
- Battery consumption

## 📱 Device-Specific Considerations

### Android Requirements
- **Minimum SDK:** 24 (Android 7.0)
- **Recommended:** Android 8.0+ for better ML Kit performance
- **RAM:** 3GB+ recommended for optimal performance
- **Storage:** Ensure sufficient space for ML Kit models

### Device Language Settings

**Important:** For optimal Khmer recognition:

1. **Add Khmer Language:**
   ```
   Settings > System > Languages & input > Languages
   Add "ខ្មែរ (Khmer)" to the language list
   ```

2. **Set Keyboard:**
   ```
   Settings > System > Languages & input > Virtual keyboard
   Add Khmer keyboard support
   ```

3. **Regional Settings:**
   ```
   Consider setting region to Cambodia for better localization
   ```

## 🛠️ Troubleshooting Common Issues

### Issue 1: No Khmer Text Detected

**Possible Causes:**
- Device doesn't support Khmer Unicode
- ML Kit model not downloaded
- Poor image quality

**Solutions:**
```dart
// Check if Khmer is supported
bool hasKhmerSupport = text.contains(RegExp(r'[\u1780-\u17FF]'));

// Verify ML Kit model availability
// Check device storage and internet connection
```

### Issue 2: Poor Recognition Accuracy

**Optimization Steps:**
1. **Image Preprocessing:**
   - Increase contrast
   - Reduce noise
   - Correct skew/rotation

2. **Capture Guidelines:**
   - Use good lighting
   - Keep camera steady
   - Ensure document is flat
   - Maintain proper distance

### Issue 3: Mixed Language Issues

**Solutions:**
```dart
// Separate Khmer and Latin text
String khmerText = KhmerTextUtils.extractKhmerText(fullText);
String latinText = KhmerTextUtils.extractLatinText(fullText);

// Process separately for better accuracy
```

## 📊 Testing Results Analysis

### Expected Output Format

```
=== ML Kit Capabilities Test ===
Total text blocks: 8
Block 0:
  Text: "ព្រះរាជាណាចក្រកម្ពុជា"
  Languages: km
  Lines: 1
  Contains Khmer: true
  Khmer character analysis:
    Count: 19
    Unicode points: 0x1796, 0x17d2, 0x179a, 0x17c7, 0x179a, 0x17b6, 0x1787, 0x17b6, 0x178e, 0x17b6, 0x1785, 0x1780, 0x17d2, 0x179a, 0x1780, 0x1798, 0x17d2, 0x1796, 0x17bb, 0x1787, 0x17b6
    Characters: ព្រះរាជាណាចក្រកម្ពុជា

Character analysis:
  Khmer characters: 45
  Latin characters: 23
  Mixed content: true
```

### Success Criteria

**High Quality Recognition (>90% accuracy):**
- All major text blocks detected
- Khmer characters properly recognized
- ID numbers correctly extracted
- Dates accurately parsed

**Acceptable Recognition (70-90% accuracy):**
- Most text blocks detected
- Some character-level errors
- Manual verification needed

**Poor Recognition (<70% accuracy):**
- Significant text missing
- High character error rate
- Requires image quality improvement

## 🚀 Next Steps

### If Native ML Kit Works Well:
1. Focus on Flutter integration optimization
2. Improve image preprocessing
3. Fine-tune extraction algorithms

### If Native ML Kit Has Issues:
1. Check device compatibility
2. Update Google Play Services
3. Consider alternative OCR solutions
4. Report issues to Google ML Kit team

### Alternative Solutions to Consider:
- **Tesseract OCR** with Khmer language pack
- **Firebase ML Kit** (cloud-based)
- **Custom trained models** for Cambodian documents
- **Hybrid approach** combining multiple OCR engines

## 📝 Documentation and Reporting

### Test Report Template:
```
Device: [Model, Android Version]
ML Kit Version: [Version]
Test Date: [Date]
Image Quality: [High/Medium/Low]
Recognition Accuracy: [Percentage]
Issues Found: [List]
Recommendations: [List]
```

### Performance Metrics:
- Average processing time: ___ms
- Memory usage peak: ___MB
- Battery consumption: ___%
- Success rate: ___%

This comprehensive testing approach will help you determine the best strategy for implementing reliable Khmer text recognition in your Flutter application.
