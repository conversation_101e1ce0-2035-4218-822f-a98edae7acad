import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../services/ocr_service.dart';
import '../services/khmer_ocr_test_service.dart';
import '../models/id_card_data_model.dart';
import '../utils/khmer_text_utils.dart';

class OCRTestScreen extends StatefulWidget {
  const OCRTestScreen({super.key});

  @override
  State<OCRTestScreen> createState() => _OCRTestScreenState();
}

class _OCRTestScreenState extends State<OCRTestScreen> {
  final OcrService _ocrService = OcrService();
  final ImagePicker _picker = ImagePicker();

  File? _selectedImage;
  String _extractedText = '';
  IDCardData? _extractedData;
  bool _isProcessing = false;
  String _analysisResults = '';

  @override
  void dispose() {
    _ocrService.dispose();
    super.dispose();
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      final XFile? image = await _picker.pickImage(
        source: source,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 90,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
          _extractedText = '';
          _extractedData = null;
          _analysisResults = '';
        });
      }
    } catch (e) {
      _showError('Error picking image: $e');
    }
  }

  Future<void> _processImage() async {
    if (_selectedImage == null) return;

    setState(() {
      _isProcessing = true;
      _analysisResults = 'Processing image...\n';
    });

    try {
      // Test Khmer support first
      final khmerTestResults = await KhmerOCRTestService.testKhmerSupport();

      // Test image recognition with Khmer focus
      final imageTestResults = await KhmerOCRTestService.testImageRecognition(
        _selectedImage!,
      );

      // Extract raw text using original service
      final text = await _ocrService.extractTextFromImage(_selectedImage!);

      // Extract structured data
      final data = await _ocrService.extractIDCardData(_selectedImage!);

      // Combine all results for analysis
      final combinedResults = {
        'khmer_support': khmerTestResults,
        'image_recognition': imageTestResults,
        'original_text': text,
        'structured_data': data,
      };

      // Generate comprehensive analysis
      final analysis = _analyzeKhmerResults(combinedResults);

      setState(() {
        _extractedText = text;
        _extractedData = data;
        _analysisResults = analysis;
        _isProcessing = false;
      });
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _analysisResults = 'Error processing image: $e';
      });
    }
  }

  String _analyzeKhmerResults(Map<String, dynamic> combinedResults) {
    final buffer = StringBuffer();

    buffer.writeln('=== COMPREHENSIVE KHMER OCR ANALYSIS ===\n');

    // Khmer Support Test Results
    if (combinedResults.containsKey('khmer_support')) {
      final khmerSupport =
          combinedResults['khmer_support'] as Map<String, dynamic>;
      buffer.writeln('📱 DEVICE KHMER SUPPORT:');

      if (khmerSupport.containsKey('device_settings')) {
        final device = khmerSupport['device_settings'] as Map<String, dynamic>;
        buffer.writeln('  Locale: ${device['locale']}');
        buffer.writeln('  Platform: ${device['platform']}');
      }

      if (khmerSupport.containsKey('unicode_support')) {
        final unicode = khmerSupport['unicode_support'] as Map<String, dynamic>;
        buffer.writeln(
          '  Unicode test results: ${unicode.length} tests passed',
        );
      }
      buffer.writeln();
    }

    // Image Recognition Results
    if (combinedResults.containsKey('image_recognition')) {
      final imageResults =
          combinedResults['image_recognition'] as Map<String, dynamic>;
      buffer.writeln('🔍 IMAGE RECOGNITION ANALYSIS:');
      buffer.writeln(
        '  Total text length: ${imageResults['text_length'] ?? 0}',
      );
      buffer.writeln(
        '  Text blocks found: ${imageResults['block_count'] ?? 0}',
      );
      buffer.writeln(
        '  Khmer characters detected: ${imageResults['khmer_char_count'] ?? 0}',
      );
      buffer.writeln('  Has Khmer text: ${imageResults['has_khmer'] ?? false}');

      if (imageResults['has_khmer'] == true) {
        buffer.writeln(
          '  Khmer text found: "${imageResults['khmer_text'] ?? ''}"',
        );
      }

      buffer.writeln(
        '  Has Cambodia keyword: ${imageResults['has_cambodia_keyword'] ?? false}',
      );
      buffer.writeln(
        '  Has KHM pattern: ${imageResults['has_khm_pattern'] ?? false}',
      );
      buffer.writeln(
        '  Has ID pattern: ${imageResults['has_id_pattern'] ?? false}',
      );
      buffer.writeln();
    }

    // Original OCR Results
    final originalText = combinedResults['original_text'] as String? ?? '';
    final structuredData = combinedResults['structured_data'] as IDCardData?;

    buffer.writeln('📝 ORIGINAL OCR RESULTS:');
    buffer.writeln('  Text: "$originalText"');
    buffer.writeln('  Length: ${originalText.length} characters');
    buffer.writeln(
      '  Contains Khmer (original): ${KhmerTextUtils.containsKhmerText(originalText)}',
    );

    if (structuredData != null) {
      buffer.writeln('\n🆔 STRUCTURED DATA EXTRACTION:');
      buffer.writeln(
        '  ID Number: ${structuredData.idNumber.isEmpty ? "❌ Not found" : "✅ ${structuredData.idNumber}"}',
      );
      buffer.writeln(
        '  Name: ${structuredData.fullName.isEmpty ? "❌ Not found" : "✅ ${structuredData.fullName}"}',
      );
      buffer.writeln(
        '  Date of Birth: ${structuredData.dateOfBirth.isEmpty ? "❌ Not found" : "✅ ${structuredData.dateOfBirth}"}',
      );
    }

    // Diagnostic Summary
    buffer.writeln('\n🎯 KHMER RECOGNITION DIAGNOSIS:');

    final hasAnyKhmer =
        (combinedResults['image_recognition'] as Map<String, dynamic>?)
                ?.containsKey('has_khmer') ==
            true &&
        (combinedResults['image_recognition']
                as Map<String, dynamic>)['has_khmer'] ==
            true;

    if (hasAnyKhmer) {
      buffer.writeln('  ✅ GOOD: Khmer text detected by ML Kit');
      buffer.writeln('  ✅ Your device supports Khmer text recognition');
    } else {
      buffer.writeln('  ⚠️ ISSUE: No Khmer text detected');
      buffer.writeln('  📋 POSSIBLE CAUSES:');
      buffer.writeln('     • Scanning the English/Latin side of ID card');
      buffer.writeln('     • Image quality too low for Khmer recognition');
      buffer.writeln('     • Device language settings need Khmer support');
      buffer.writeln('     • ML Kit model may need to download Khmer support');
    }

    buffer.writeln('\n💡 RECOMMENDATIONS:');
    buffer.writeln('  1. Try scanning the Khmer side of the ID card');
    buffer.writeln('  2. Ensure good lighting and focus');
    buffer.writeln('  3. Add Khmer language to device settings');
    buffer.writeln('  4. Update Google Play Services');
    buffer.writeln('  5. Test with a clear Khmer text image');

    return buffer.toString();
  }

  String _analyzeResults(String text, IDCardData data) {
    final buffer = StringBuffer();

    buffer.writeln('=== OCR ANALYSIS RESULTS ===\n');

    // Basic text analysis
    buffer.writeln('📝 TEXT ANALYSIS:');
    buffer.writeln('Total characters: ${text.length}');
    buffer.writeln('Contains Khmer: ${KhmerTextUtils.containsKhmerText(text)}');

    if (KhmerTextUtils.containsKhmerText(text)) {
      final khmerText = KhmerTextUtils.extractKhmerText(text);
      final latinText = KhmerTextUtils.extractLatinText(text);
      buffer.writeln('Khmer characters: ${khmerText.length}');
      buffer.writeln('Latin characters: ${latinText.length}');
      buffer.writeln('Khmer text: "$khmerText"');
      buffer.writeln('Latin text: "$latinText"');
    }

    buffer.writeln('\n🆔 EXTRACTED DATA:');
    buffer.writeln(
      'ID Number: ${data.idNumber.isEmpty ? "❌ Not found" : "✅ ${data.idNumber}"}',
    );
    buffer.writeln(
      'Full Name: ${data.fullName.isEmpty ? "❌ Not found" : "✅ ${data.fullName}"}',
    );
    buffer.writeln(
      'Date of Birth: ${data.dateOfBirth.isEmpty ? "❌ Not found" : "✅ ${data.dateOfBirth}"}',
    );
    buffer.writeln(
      'Gender: ${data.gender?.isEmpty ?? true ? "❌ Not found" : "✅ ${data.gender}"}',
    );
    buffer.writeln(
      'Nationality: ${data.nationality?.isEmpty ?? true ? "❌ Not found" : "✅ ${data.nationality}"}',
    );
    buffer.writeln(
      'Address: ${data.address?.isEmpty ?? true ? "❌ Not found" : "✅ ${data.address}"}',
    );

    // Calculate extraction success rate
    int successCount = 0;
    int totalFields = 6;

    if (data.idNumber.isNotEmpty) successCount++;
    if (data.fullName.isNotEmpty) successCount++;
    if (data.dateOfBirth.isNotEmpty) successCount++;
    if (data.gender?.isNotEmpty ?? false) successCount++;
    if (data.nationality?.isNotEmpty ?? false) successCount++;
    if (data.address?.isNotEmpty ?? false) successCount++;

    final successRate = (successCount / totalFields * 100).round();
    buffer.writeln(
      '\n📊 SUCCESS RATE: $successRate% ($successCount/$totalFields fields)',
    );

    // Quality assessment
    buffer.writeln('\n🎯 QUALITY ASSESSMENT:');
    if (successRate >= 90) {
      buffer.writeln('✅ Excellent - High quality recognition');
    } else if (successRate >= 70) {
      buffer.writeln('⚠️ Good - Some fields may need verification');
    } else if (successRate >= 50) {
      buffer.writeln('⚠️ Fair - Multiple fields missing or incorrect');
    } else {
      buffer.writeln('❌ Poor - Consider retaking the image');
    }

    // Recommendations
    buffer.writeln('\n💡 RECOMMENDATIONS:');
    if (text.length < 50) {
      buffer.writeln('• Image may be too blurry or low quality');
    }
    if (!KhmerTextUtils.containsKhmerText(text) && data.idNumber.isEmpty) {
      buffer.writeln('• This may not be a Cambodian ID card');
    }
    if (data.idNumber.isEmpty) {
      buffer.writeln('• Ensure ID number area is clearly visible');
    }
    if (data.fullName.isEmpty) {
      buffer.writeln('• Ensure name area has good lighting');
    }
    if (data.dateOfBirth.isEmpty) {
      buffer.writeln('• Check if date of birth is clearly readable');
    }

    return buffer.toString();
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('OCR Testing'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Image selection buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _pickImage(ImageSource.camera),
                    icon: const Icon(Icons.camera_alt),
                    label: const Text('Camera'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _pickImage(ImageSource.gallery),
                    icon: const Icon(Icons.photo_library),
                    label: const Text('Gallery'),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Selected image display
            if (_selectedImage != null) ...[
              Container(
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.file(_selectedImage!, fit: BoxFit.contain),
                ),
              ),

              const SizedBox(height: 16),

              // Process button
              ElevatedButton.icon(
                onPressed: _isProcessing ? null : _processImage,
                icon:
                    _isProcessing
                        ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Icon(Icons.text_fields),
                label: Text(_isProcessing ? 'Processing...' : 'Extract Text'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ],

            const SizedBox(height: 16),

            // Results display
            if (_analysisResults.isNotEmpty) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Analysis Results',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          _analysisResults,
                          style: const TextStyle(
                            fontFamily: 'monospace',
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],

            // Raw text display
            if (_extractedText.isNotEmpty) ...[
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Raw Extracted Text',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          _extractedText.isEmpty
                              ? 'No text extracted'
                              : _extractedText,
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
