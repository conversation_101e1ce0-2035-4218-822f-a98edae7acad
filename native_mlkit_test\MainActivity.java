package com.example.mlkittest;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.util.Log;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.google.android.gms.tasks.OnFailureListener;
import com.google.android.gms.tasks.OnSuccessListener;
import com.google.mlkit.vision.common.InputImage;
import com.google.mlkit.vision.text.Text;
import com.google.mlkit.vision.text.TextRecognition;
import com.google.mlkit.vision.text.TextRecognizer;
import com.google.mlkit.vision.text.latin.TextRecognizerOptions;

import java.io.IOException;

public class MainActivity extends AppCompatActivity {
    private static final String TAG = "MLKitTest";
    private static final int REQUEST_IMAGE_CAPTURE = 1;
    private static final int REQUEST_IMAGE_PICK = 2;
    private static final int REQUEST_CAMERA_PERMISSION = 100;

    private ImageView imageView;
    private TextView resultTextView;
    private Button captureButton;
    private Button pickButton;
    private Button testKhmerButton;
    
    private TextRecognizer textRecognizer;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // Initialize views
        imageView = findViewById(R.id.imageView);
        resultTextView = findViewById(R.id.resultTextView);
        captureButton = findViewById(R.id.captureButton);
        pickButton = findViewById(R.id.pickButton);
        testKhmerButton = findViewById(R.id.testKhmerButton);

        // Initialize ML Kit Text Recognizer
        textRecognizer = TextRecognition.getClient(TextRecognizerOptions.DEFAULT_OPTIONS);

        // Set up button listeners
        captureButton.setOnClickListener(v -> {
            if (checkCameraPermission()) {
                captureImage();
            } else {
                requestCameraPermission();
            }
        });

        pickButton.setOnClickListener(v -> pickImage());
        testKhmerButton.setOnClickListener(v -> testKhmerRecognition());

        Log.d(TAG, "ML Kit Text Recognizer initialized");
    }

    private boolean checkCameraPermission() {
        return ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) 
               == PackageManager.PERMISSION_GRANTED;
    }

    private void requestCameraPermission() {
        ActivityCompat.requestPermissions(this, 
            new String[]{Manifest.permission.CAMERA}, 
            REQUEST_CAMERA_PERMISSION);
    }

    private void captureImage() {
        Intent takePictureIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
        if (takePictureIntent.resolveActivity(getPackageManager()) != null) {
            startActivityForResult(takePictureIntent, REQUEST_IMAGE_CAPTURE);
        }
    }

    private void pickImage() {
        Intent intent = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
        startActivityForResult(intent, REQUEST_IMAGE_PICK);
    }

    private void testKhmerRecognition() {
        // Log information about ML Kit capabilities
        Log.d(TAG, "=== ML Kit Khmer Text Recognition Test ===");
        Log.d(TAG, "TextRecognizer Options: " + TextRecognizerOptions.DEFAULT_OPTIONS);
        
        resultTextView.setText("Testing Khmer recognition capabilities...\n\n" +
            "ML Kit Text Recognition supports:\n" +
            "- Latin script\n" +
            "- Mixed language text\n" +
            "- Various fonts and styles\n\n" +
            "For Khmer text:\n" +
            "- Unicode range: U+1780-U+17FF\n" +
            "- May require specific device language settings\n" +
            "- Performance varies by device\n\n" +
            "Please capture or select an image with Khmer text to test.");
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        if (resultCode == RESULT_OK) {
            Bitmap bitmap = null;
            
            try {
                if (requestCode == REQUEST_IMAGE_CAPTURE && data != null) {
                    bitmap = (Bitmap) data.getExtras().get("data");
                } else if (requestCode == REQUEST_IMAGE_PICK && data != null) {
                    Uri imageUri = data.getData();
                    bitmap = MediaStore.Images.Media.getBitmap(getContentResolver(), imageUri);
                }
                
                if (bitmap != null) {
                    imageView.setImageBitmap(bitmap);
                    recognizeText(bitmap);
                }
            } catch (IOException e) {
                Log.e(TAG, "Error loading image", e);
                Toast.makeText(this, "Error loading image", Toast.LENGTH_SHORT).show();
            }
        }
    }

    private void recognizeText(Bitmap bitmap) {
        InputImage image = InputImage.fromBitmap(bitmap, 0);
        
        Log.d(TAG, "Starting text recognition...");
        resultTextView.setText("Processing image...");
        
        textRecognizer.process(image)
            .addOnSuccessListener(new OnSuccessListener<Text>() {
                @Override
                public void onSuccess(Text visionText) {
                    processTextResult(visionText);
                }
            })
            .addOnFailureListener(new OnFailureListener() {
                @Override
                public void onFailure(@NonNull Exception e) {
                    Log.e(TAG, "Text recognition failed", e);
                    resultTextView.setText("Text recognition failed: " + e.getMessage());
                }
            });
    }

    private void processTextResult(Text visionText) {
        String resultText = visionText.getText();
        
        Log.d(TAG, "=== Text Recognition Results ===");
        Log.d(TAG, "Full text: " + resultText);
        
        StringBuilder analysis = new StringBuilder();
        analysis.append("=== RECOGNITION RESULTS ===\n\n");
        analysis.append("Full Text:\n").append(resultText).append("\n\n");
        
        // Analyze for Khmer characters
        boolean hasKhmer = containsKhmerText(resultText);
        analysis.append("Contains Khmer: ").append(hasKhmer).append("\n");
        
        if (hasKhmer) {
            String khmerText = extractKhmerText(resultText);
            analysis.append("Khmer text: ").append(khmerText).append("\n");
        }
        
        // Analyze text blocks
        analysis.append("\n=== TEXT BLOCKS ===\n");
        for (Text.TextBlock block : visionText.getTextBlocks()) {
            analysis.append("Block: ").append(block.getText()).append("\n");
            
            for (Text.Line line : block.getLines()) {
                analysis.append("  Line: ").append(line.getText()).append("\n");
                
                for (Text.Element element : line.getElements()) {
                    analysis.append("    Element: ").append(element.getText()).append("\n");
                }
            }
        }
        
        resultTextView.setText(analysis.toString());
        Log.d(TAG, analysis.toString());
    }

    private boolean containsKhmerText(String text) {
        // Khmer Unicode range: U+1780 to U+17FF
        return text.matches(".*[\\u1780-\\u17FF].*");
    }

    private String extractKhmerText(String text) {
        return text.replaceAll("[^\\u1780-\\u17FF]", "");
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, 
                                         @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        
        if (requestCode == REQUEST_CAMERA_PERMISSION) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                captureImage();
            } else {
                Toast.makeText(this, "Camera permission required", Toast.LENGTH_SHORT).show();
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (textRecognizer != null) {
            textRecognizer.close();
        }
    }
}
