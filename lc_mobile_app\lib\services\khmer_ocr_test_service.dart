import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';

class KhmerOCRTestService {
  static const bool _enableDetailedLogging = true;
  
  // Test Khmer text recognition capabilities
  static Future<Map<String, dynamic>> testKhmerSupport() async {
    final results = <String, dynamic>{};
    
    if (_enableDetailedLogging) {
      debugPrint('=== COMPREHENSIVE KHMER SUPPORT TEST ===');
    }
    
    // Test 1: Device Khmer Unicode Support
    results['unicode_support'] = await _testUnicodeSupport();
    
    // Test 2: ML Kit Configuration
    results['mlkit_config'] = await _testMLKitConfiguration();
    
    // Test 3: Device Language Settings
    results['device_settings'] = await _testDeviceSettings();
    
    // Test 4: Text Recognition Capabilities
    results['recognition_capabilities'] = await _testRecognitionCapabilities();
    
    if (_enableDetailedLogging) {
      debugPrint('=== KHMER SUPPORT TEST COMPLETE ===');
      debugPrint('Results: $results');
    }
    
    return results;
  }
  
  static Future<Map<String, dynamic>> _testUnicodeSupport() async {
    debugPrint('--- Testing Unicode Support ---');
    
    final testTexts = {
      'kingdom_cambodia': 'ព្រះរាជាណាចក្រកម្ពុជា',
      'identity_card': 'អត្តសញ្ញាណប័ណ្ណ',
      'name': 'ឈ្មោះ',
      'date_birth': 'ថ្ងៃខែឆ្នាំកំណើត',
      'address': 'អាសយដ្ឋាន',
      'common_name': 'សុខ ចាន់ថា',
    };
    
    final results = <String, dynamic>{};
    
    for (final entry in testTexts.entries) {
      final key = entry.key;
      final text = entry.value;
      
      final analysis = {
        'text': text,
        'length': text.length,
        'rune_count': text.runes.length,
        'unicode_points': text.runes.map((r) => '0x${r.toRadixString(16)}').toList(),
        'has_khmer_chars': text.runes.any((rune) => rune >= 0x1780 && rune <= 0x17FF),
        'khmer_char_count': text.runes.where((rune) => rune >= 0x1780 && rune <= 0x17FF).length,
      };
      
      results[key] = analysis;
      
      if (_enableDetailedLogging) {
        debugPrint('$key: ${analysis['text']}');
        debugPrint('  Unicode points: ${analysis['unicode_points']}');
        debugPrint('  Khmer chars: ${analysis['khmer_char_count']}/${analysis['rune_count']}');
      }
    }
    
    return results;
  }
  
  static Future<Map<String, dynamic>> _testMLKitConfiguration() async {
    debugPrint('--- Testing ML Kit Configuration ---');
    
    final results = <String, dynamic>{};
    
    try {
      // Test different TextRecognizer configurations
      final defaultRecognizer = TextRecognizer();
      results['default_recognizer'] = 'initialized';
      
      // Test if we can create recognizer successfully
      results['recognizer_creation'] = 'success';
      
      // Clean up
      defaultRecognizer.close();
      
      if (_enableDetailedLogging) {
        debugPrint('ML Kit TextRecognizer: OK');
      }
      
    } catch (e) {
      results['error'] = e.toString();
      if (_enableDetailedLogging) {
        debugPrint('ML Kit Error: $e');
      }
    }
    
    return results;
  }
  
  static Future<Map<String, dynamic>> _testDeviceSettings() async {
    debugPrint('--- Testing Device Settings ---');
    
    final results = <String, dynamic>{};
    
    // Get device locale
    results['locale'] = Platform.localeName;
    results['platform'] = Platform.operatingSystem;
    results['version'] = Platform.operatingSystemVersion;
    
    if (_enableDetailedLogging) {
      debugPrint('Device locale: ${results['locale']}');
      debugPrint('Platform: ${results['platform']}');
      debugPrint('OS Version: ${results['version']}');
    }
    
    return results;
  }
  
  static Future<Map<String, dynamic>> _testRecognitionCapabilities() async {
    debugPrint('--- Testing Recognition Capabilities ---');
    
    final results = <String, dynamic>{};
    
    try {
      final recognizer = TextRecognizer();
      
      // Test with a simple synthetic image (we'll create this)
      results['recognizer_ready'] = true;
      
      // Note: For actual image testing, we would need to:
      // 1. Create a synthetic image with Khmer text
      // 2. Or use a known test image
      // 3. Process it through ML Kit
      
      results['test_status'] = 'ready_for_image_testing';
      
      recognizer.close();
      
    } catch (e) {
      results['error'] = e.toString();
      if (_enableDetailedLogging) {
        debugPrint('Recognition test error: $e');
      }
    }
    
    return results;
  }
  
  // Test with actual image
  static Future<Map<String, dynamic>> testImageRecognition(File imageFile) async {
    debugPrint('=== TESTING IMAGE RECOGNITION ===');
    
    final results = <String, dynamic>{};
    final recognizer = TextRecognizer();
    
    try {
      final inputImage = InputImage.fromFile(imageFile);
      final recognizedText = await recognizer.processImage(inputImage);
      
      final fullText = recognizedText.text;
      
      // Analyze the results
      results['full_text'] = fullText;
      results['text_length'] = fullText.length;
      results['block_count'] = recognizedText.blocks.length;
      
      // Check for Khmer characters
      final khmerChars = fullText.runes.where((rune) => rune >= 0x1780 && rune <= 0x17FF).toList();
      results['khmer_char_count'] = khmerChars.length;
      results['has_khmer'] = khmerChars.isNotEmpty;
      
      if (khmerChars.isNotEmpty) {
        results['khmer_text'] = String.fromCharCodes(khmerChars);
        results['khmer_unicode_points'] = khmerChars.map((r) => '0x${r.toRadixString(16)}').toList();
      }
      
      // Analyze text blocks
      final blockAnalysis = <Map<String, dynamic>>[];
      for (int i = 0; i < recognizedText.blocks.length; i++) {
        final block = recognizedText.blocks[i];
        final blockKhmerChars = block.text.runes.where((rune) => rune >= 0x1780 && rune <= 0x17FF).length;
        
        blockAnalysis.add({
          'index': i,
          'text': block.text,
          'length': block.text.length,
          'khmer_chars': blockKhmerChars,
          'has_khmer': blockKhmerChars > 0,
          'line_count': block.lines.length,
        });
      }
      results['blocks'] = blockAnalysis;
      
      // Check for common Cambodian patterns
      results['has_cambodia_keyword'] = fullText.toLowerCase().contains('cambodia');
      results['has_khm_pattern'] = fullText.contains('KHM');
      results['has_id_pattern'] = RegExp(r'[A-Z]\d{10,}').hasMatch(fullText);
      
      if (_enableDetailedLogging) {
        debugPrint('Image recognition results:');
        debugPrint('  Text length: ${results['text_length']}');
        debugPrint('  Blocks: ${results['block_count']}');
        debugPrint('  Khmer chars: ${results['khmer_char_count']}');
        debugPrint('  Has Khmer: ${results['has_khmer']}');
        if (results['has_khmer']) {
          debugPrint('  Khmer text: "${results['khmer_text']}"');
        }
      }
      
    } catch (e) {
      results['error'] = e.toString();
      if (_enableDetailedLogging) {
        debugPrint('Image recognition error: $e');
      }
    } finally {
      recognizer.close();
    }
    
    return results;
  }
  
  // Generate diagnostic report
  static String generateDiagnosticReport(Map<String, dynamic> testResults) {
    final buffer = StringBuffer();
    
    buffer.writeln('=== KHMER OCR DIAGNOSTIC REPORT ===\n');
    
    // Unicode Support
    if (testResults.containsKey('unicode_support')) {
      buffer.writeln('📝 UNICODE SUPPORT:');
      final unicode = testResults['unicode_support'] as Map<String, dynamic>;
      for (final entry in unicode.entries) {
        final analysis = entry.value as Map<String, dynamic>;
        buffer.writeln('  ${entry.key}: ${analysis['khmer_char_count']} Khmer chars');
      }
      buffer.writeln();
    }
    
    // Device Settings
    if (testResults.containsKey('device_settings')) {
      buffer.writeln('📱 DEVICE SETTINGS:');
      final device = testResults['device_settings'] as Map<String, dynamic>;
      buffer.writeln('  Locale: ${device['locale']}');
      buffer.writeln('  Platform: ${device['platform']}');
      buffer.writeln('  OS Version: ${device['version']}');
      buffer.writeln();
    }
    
    // ML Kit Configuration
    if (testResults.containsKey('mlkit_config')) {
      buffer.writeln('🔧 ML KIT STATUS:');
      final mlkit = testResults['mlkit_config'] as Map<String, dynamic>;
      if (mlkit.containsKey('error')) {
        buffer.writeln('  ❌ Error: ${mlkit['error']}');
      } else {
        buffer.writeln('  ✅ Configuration: OK');
      }
      buffer.writeln();
    }
    
    // Recognition Results (if available)
    if (testResults.containsKey('recognition_results')) {
      buffer.writeln('🔍 RECOGNITION RESULTS:');
      final recognition = testResults['recognition_results'] as Map<String, dynamic>;
      buffer.writeln('  Text length: ${recognition['text_length']}');
      buffer.writeln('  Khmer chars: ${recognition['khmer_char_count']}');
      buffer.writeln('  Has Khmer: ${recognition['has_khmer']}');
      buffer.writeln();
    }
    
    // Recommendations
    buffer.writeln('💡 RECOMMENDATIONS:');
    buffer.writeln('1. Ensure device has Khmer language support enabled');
    buffer.writeln('2. Check if Google Play Services is updated');
    buffer.writeln('3. Try scanning the Khmer side of the ID card');
    buffer.writeln('4. Ensure good lighting and image quality');
    buffer.writeln('5. Consider using different ML Kit language models');
    
    return buffer.toString();
  }
}
