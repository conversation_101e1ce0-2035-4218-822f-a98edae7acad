<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="ML Kit Khmer Text Recognition Test"
        android:textSize="18sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <Button
            android:id="@+id/captureButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Capture Image"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/pickButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Pick Image"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <Button
        android:id="@+id/testKhmerButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Test Khmer Recognition Info"
        android:layout_marginBottom="16dp" />

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:scaleType="centerCrop"
        android:background="#f0f0f0"
        android:layout_marginBottom="16dp" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <TextView
            android:id="@+id/resultTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Select or capture an image to test text recognition"
            android:textSize="12sp"
            android:fontFamily="monospace"
            android:padding="8dp"
            android:background="#f8f8f8" />

    </ScrollView>

</LinearLayout>
